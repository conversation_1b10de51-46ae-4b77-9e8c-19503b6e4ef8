import os
import sys

def run_detect():
    """简化版车牌检测程序"""
    # 添加plate-main目录到Python路径
    plate_main_path = os.path.join(os.path.dirname(__file__), 'plate-main')
    sys.path.insert(0, plate_main_path)
    
    # 切换到plate-main目录以确保相对路径正确
    original_dir = os.getcwd()
    os.chdir(plate_main_path)
    
    try:
        # 导入所需模块
        import argparse
        
        # 使用相对导入或直接导入detect1模块
        import importlib
        detect1 = importlib.import_module('detect1')
        detect = detect1.detect
        
        # 创建参数
        parser = argparse.ArgumentParser()
        parser.add_argument('--weights', nargs='+', type=str, default='weights/last.pt')
        parser.add_argument('--source', type=str, default='../test0.jpg')
        parser.add_argument('--output', type=str, default='inference/output')
        parser.add_argument('--img-size', type=int, default=640)
        parser.add_argument('--conf-thres', type=float, default=0.4)
        parser.add_argument('--iou-thres', type=float, default=0.5)
        parser.add_argument('--device', default='')
        parser.add_argument('--view-img', action='store_true')
        parser.add_argument('--save-txt', action='store_true')
        parser.add_argument('--classes', nargs='+', type=int)
        parser.add_argument('--agnostic-nms', action='store_true')
        parser.add_argument('--augment', action='store_true')
        parser.add_argument('--update', action='store_true')
        
        # 设置参数并运行检测
        opt = parser.parse_args([])
        
        # 为detect1模块创建opt对象
        import types
        detect1_opt = types.SimpleNamespace()
        detect1_opt.output = opt.output
        detect1_opt.source = opt.source
        detect1_opt.weights = opt.weights
        detect1_opt.view_img = opt.view_img
        detect1_opt.save_txt = opt.save_txt
        detect1_opt.img_size = opt.img_size
        detect1_opt.conf_thres = opt.conf_thres
        detect1_opt.iou_thres = opt.iou_thres
        detect1_opt.device = opt.device
        detect1_opt.classes = opt.classes
        detect1_opt.agnostic_nms = opt.agnostic_nms
        detect1_opt.augment = opt.augment
        detect1_opt.update = opt.update
        
        # 设置detect1模块的opt
        detect1.opt = detect1_opt
        
        print("正在运行车牌检测...")
        detect()
        print("检测完成！结果已保存到inference/output目录")
        
    finally:
        # 恢复原始目录
        os.chdir(original_dir)

if __name__ == "__main__":
    run_detect()